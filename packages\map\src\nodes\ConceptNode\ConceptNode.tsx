import { useState, useCallback, useEffect, useRef } from 'react';
import { NodeProps, useReactFlow, useUpdateNodeInternals } from '@xyflow/react';
import { NodeHandles } from './edges/Handles';
import { NodeTitleInput } from './nodesection/NodeTitle';
import { NodeTagsList } from './nodetags/TagsList';
import { NodeSections } from './nodesection/SectionsMap';
import { NodeAddSectionButton } from './nodesection/AddSectionButton';
import { motion } from 'framer-motion';
import { SectionConfig } from '../../Map/interfaces/SectionConfig';
import { MapNodeData } from '../../Map/interfaces/MapNodeData';
import { useNodeStore } from '../NodeStore';

interface ConceptNodeProps extends NodeProps {
    debouncedUpdateMap?: () => void;
}

export const ConceptNode = ({ id, data, parentId, debouncedUpdateMap }: ConceptNodeProps) => {
    console.log('[ConceptNode] Rendering ConceptNode with id:', id);
    console.log('[ConceptNode] Node data:', data);
    console.log('[ConceptNode] Parent ID:', parentId);

    const reactFlowInstance = useReactFlow();
    const updateNodeInternals = useUpdateNodeInternals();
    const nodeRef = useRef<HTMLDivElement>(null);
    const nodeStore = useNodeStore();

    // Extract data from node or use defaults with proper type casting
    const nodeData = data as unknown as MapNodeData;
    const conceptTitle = nodeData?.conceptTitle || '';
    const cardCount = nodeData?.cardCount || 0;
    const sections = nodeData?.sections || ({} as Record<string, SectionConfig>);
    const tags = nodeData?.tags || ([] as { name: string; type: string }[]);

    // State for UI interactions
    const [ctrlPressed, setCtrlPressed] = useState(false);

    // Set the showAddBar state in the store when mouse enters/leaves
    const handleMouseEnter = () => nodeStore.setShowAddBar(id as string, true);
    const handleMouseLeave = () => nodeStore.setShowAddBar(id as string, false);

    // Get the showAddBar state from the store
    const showAddBar = useNodeStore(state => state.showAddBar[id as string] || false);
    const loadingSections = useNodeStore(state => state.loadingSections);
    const generationError = useNodeStore(state => state.generationError);

    // Add event listeners for CTRL key press/release
    useEffect(() => {
        // Only add listeners if the node is in a group
        if (!parentId) return;

        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Control') {
                setCtrlPressed(true);
                console.log(`[ConceptNode] CTRL key pressed for node ${id}`);
            }
        };

        const handleKeyUp = (e: KeyboardEvent) => {
            if (e.key === 'Control') {
                setCtrlPressed(false);
                console.log(`[ConceptNode] CTRL key released for node ${id}`);
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);

        return () => {
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
        };
    }, [id, parentId]);

    // Use the updateNodeData function from the NodeStore
    const updateNodeData = useCallback(
        (newData: Record<string, any>) => {
            nodeStore.updateNodeData(id as string, newData, debouncedUpdateMap);
        },
        [id, nodeStore, debouncedUpdateMap]
    );

    // Use the startEditingSectionLabel function from the NodeStore
    const startEditingSectionLabel = useCallback(
        (sectionId: string, label: string) => {
            nodeStore.startEditingSectionLabel(sectionId, label);
        },
        [nodeStore]
    );

    // Use the generateSectionContentWithLLM function from the NodeStore
    const generateSectionContentWithLLM = useCallback(
        async (section: SectionConfig) => {
            await nodeStore.generateSectionContentWithLLM(
                id as string,
                section,
                conceptTitle,
                sections,
                debouncedUpdateMap
            );
        },
        [id, conceptTitle, sections, nodeStore, debouncedUpdateMap]
    );

    // Create wrapper functions for loading state setters to match expected signature
    const setLoadingSections = useCallback(
        (updater: (prev: Record<string, boolean>) => Record<string, boolean>) => {
            const currentLoadingSections = useNodeStore.getState().loadingSections;
            const newLoadingSections = updater(currentLoadingSections);

            // Update each section's loading state individually
            Object.entries(newLoadingSections).forEach(([sectionId, isLoading]) => {
                if (currentLoadingSections[sectionId] !== isLoading) {
                    nodeStore.setLoadingSection(sectionId, isLoading);
                }
            });
        },
        [nodeStore]
    );

    const setGenerationError = useCallback(
        (updater: (prev: Record<string, string>) => Record<string, string>) => {
            const currentErrors = useNodeStore.getState().generationError;
            const newErrors = updater(currentErrors);

            // Update each section's error state individually
            Object.entries(newErrors).forEach(([sectionId, error]) => {
                if (currentErrors[sectionId] !== error) {
                    nodeStore.setGenerationError(sectionId, error);
                }
            });

            // Handle deleted errors
            Object.keys(currentErrors).forEach(sectionId => {
                if (!(sectionId in newErrors)) {
                    nodeStore.setGenerationError(sectionId, null);
                }
            });
        },
        [nodeStore]
    );

    // Log z-index and position information
    const zIndexValue = parentId ? 5 : 0;

    // Update node internals to ensure handles are properly registered
    useEffect(() => {
        // Update node internals when the component mounts
        updateNodeInternals(id as string);

        // Instead of an interval, use a few strategic timeouts
        // This reduces the number of updates while still ensuring handles are registered
        const timeoutIds = [
            setTimeout(() => updateNodeInternals(id as string), 500),
            setTimeout(() => updateNodeInternals(id as string), 1000),
            setTimeout(() => updateNodeInternals(id as string), 2000)
        ];

        return () => {
            // Clean up all timeouts
            timeoutIds.forEach(timeoutId => clearTimeout(timeoutId));
        };
    }, [id, updateNodeInternals]);

    // Log detailed position information - only on mount to reduce unnecessary renders
    useEffect(() => {
        if (parentId) {
            const nodes = reactFlowInstance.getNodes();
            const parentNode = nodes.find(node => node.id === parentId);
            const nodePosition = reactFlowInstance.getNode(id as string)?.position;

            console.log(`[ConceptNode] Node ${id} position details:`, {
                nodePosition: nodePosition ? { x: nodePosition.x, y: nodePosition.y } : 'unknown',
                parentPosition: parentNode
                    ? { x: parentNode.position.x, y: parentNode.position.y }
                    : 'unknown',
                parentDimensions: parentNode
                    ? {
                          width: parentNode.style?.width || 'default',
                          height: parentNode.style?.height || 'default'
                      }
                    : 'unknown',
                absolutePosition:
                    parentNode && nodePosition
                        ? {
                              x: nodePosition.x + parentNode.position.x,
                              y: nodePosition.y + parentNode.position.y
                          }
                        : 'unknown'
            });
        }
    }, []); // Empty dependency array to run only once on mount

    return (
        <motion.div
            ref={nodeRef}
            className={`concept-node-container min-h-[250px] w-[440px] min-w-[440px] max-w-[440px] rounded-lg border ${
                parentId && ctrlPressed ? 'border-2 border-blue-500' : 'border-gray-200'
            } relative overflow-hidden bg-white shadow-lg hover:border-gray-300`}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            transition={{
                duration: 0.1,
                ease: [0.04, 0.62, 0.23, 0.98]
            }}
            style={{
                position: 'relative',
                zIndex: zIndexValue, // Higher z-index when in a parent
                background: 'white', // Ensure white background
                borderColor: parentId && ctrlPressed ? '#3b82f6' : '#e5e7eb', // Blue border when CTRL is pressed
                boxShadow:
                    parentId && ctrlPressed ? '0 0 0 2px rgba(59, 130, 246, 0.5)' : undefined, // Blue glow when CTRL is pressed
                pointerEvents: 'none', // Disable dragging on the entire node
                boxSizing: 'border-box', // Ensure padding doesn't affect width
                maxWidth: '440px',
                width: '440px'
            }}
            onLoad={() => {
                // Update node internals when the div is loaded
                updateNodeInternals(id as string);
            }}
        >
            {/* Drag handle area - only this area allows dragging */}
            <div
                className='absolute left-0 right-0 top-0 h-8 cursor-move bg-transparent'
                style={{
                    pointerEvents: 'auto' // Enable dragging only on this area
                }}
                title='Drag to move node'
            />

            {/* Content area - non-draggable */}
            <div
                className='nodrag relative w-full max-w-full overflow-hidden p-4'
                style={{
                    pointerEvents: 'auto', // Enable interactions but not dragging
                    maxWidth: '100%',
                    width: '100%'
                }}
            >
                <NodeHandles />
                <NodeTitleInput
                    conceptTitle={conceptTitle}
                    cardCount={cardCount}
                    updateNodeData={updateNodeData}
                />
                <NodeTagsList id={id as string} showAddBar={showAddBar} />
                <NodeSections
                    sections={sections}
                    loadingSections={loadingSections}
                    generationError={generationError}
                    updateNodeData={updateNodeData}
                    generateSectionContentWithLLM={generateSectionContentWithLLM}
                />
                <NodeAddSectionButton
                    conceptTitle={conceptTitle}
                    sections={sections}
                    tags={tags}
                    showAddBar={showAddBar}
                    startEditingSectionLabel={startEditingSectionLabel}
                    generateSectionContentWithLLM={generateSectionContentWithLLM}
                    updateNodeData={updateNodeData}
                    nodeId={id as string}
                    setLoadingSections={setLoadingSections}
                    setGenerationError={setGenerationError}
                />
            </div>
        </motion.div>
    );
};
