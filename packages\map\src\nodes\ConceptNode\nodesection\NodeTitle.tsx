import React, { useState } from 'react';
import { BookOpen } from 'lucide-react';

type NodeTitleInputProps = {
    conceptTitle: string;
    cardCount: number;
    updateNodeData: (data: Record<string, any>) => void;
};

export const NodeTitleInput = ({
    conceptTitle,
    cardCount,
    updateNodeData
}: NodeTitleInputProps) => {
    const [editingTitle, setEditingTitle] = useState(false);
    const [titleValue, setTitleValue] = useState(conceptTitle);

    // Update local state when prop changes
    React.useEffect(() => {
        setTitleValue(conceptTitle);
    }, [conceptTitle]);

    const handleTitleFocus = () => {
        setEditingTitle(true);
    };

    const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setTitleValue(event.target.value);
    };

    const saveTitleEdit = (event: React.FocusEvent<HTMLInputElement>) => {
        let newTitle = titleValue.trim();

        // If the title is empty, set it to "New Concept" to ensure we always have a title
        if (!newTitle) {
            newTitle = 'New Concept';
            setTitleValue(newTitle);
        }

        if (newTitle !== conceptTitle) {
            console.log(
                `[TitleInput.tsx:saveTitleEdit] Updating title from "${conceptTitle}" to "${newTitle}"`
            );
            updateNodeData({ conceptTitle: newTitle });

            // Force a save to Triplit after a short delay to ensure the update is processed
            setTimeout(() => {
                console.log(`[TitleInput.tsx:saveTitleEdit] Forcing save to Triplit`);
                updateNodeData({ lastUpdated: new Date().toISOString() });
            }, 100);
        }
        setEditingTitle(false);
    };

    return (
        <div className='mb-2 flex items-center justify-between'>
            {/* Main title input: controlled component, save on blur */}
            <input
                type='text'
                value={titleValue}
                onChange={handleTitleChange}
                onBlur={saveTitleEdit}
                onFocus={handleTitleFocus}
                onKeyDown={e => {
                    if (e.key === 'Enter') e.currentTarget.blur();
                }}
                className={`w-full bg-transparent text-base font-medium text-gray-900 transition-all duration-200 focus:bg-gray-50/50 focus:outline-none ${
                    editingTitle
                        ? '-m-px rounded-sm p-px ring-1 ring-inset ring-blue-300'
                        : 'cursor-text'
                }`}
                placeholder='New Concept Title'
            />
            <div className='flex items-center'>
                {/* Card Count Button */}
                <button
                    className='flex items-center gap-1 rounded-md bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-200'
                    onClick={() =>
                        console.log(
                            '[packages/ui/map/features/conceptnode/components/NodeTitleInput.tsx] Card count clicked'
                        )
                    }
                >
                    <BookOpen size={12} />
                    <span>{cardCount}</span>
                </button>
            </div>
        </div>
    );
};
