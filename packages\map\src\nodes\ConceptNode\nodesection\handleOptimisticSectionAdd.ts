import { NODE_SECTIONS } from '@repo/utils';
import { SectionConfig } from '../../../Map/interfaces/SectionConfig';
import { useApiStore, NodeSection, NodeTag } from '@repo/api';

/**
 * Optimistically adds a section to the node and starts content generation
 * This provides immediate UI feedback while content is being generated
 */
export const handleOptimisticSectionAdd = async (
    sectionKey: string,
    conceptTitle: string,
    sections: Record<string, SectionConfig>,
    tags: { name: string; type: string }[],
    nodeId: string,
    updateNodeData: (data: Record<string, any>) => void,
    setLoadingSections: (updater: (prev: Record<string, boolean>) => Record<string, boolean>) => void,
    setGenerationError: (updater: (prev: Record<string, string>) => Record<string, string>) => void
): Promise<SectionConfig> => {
    const sectionDefaults = NODE_SECTIONS[sectionKey as keyof typeof NODE_SECTIONS];
    
    if (!sectionDefaults) {
        throw new Error(`Unknown section key: ${sectionKey}`);
    }

    // Create section immediately with default title for optimistic UI
    const sectionId = crypto.randomUUID();
    const newSection: SectionConfig = {
        id: sectionId,
        label: sectionDefaults.defaultTitle,
        icon: sectionDefaults.icon,
        value: '', // Empty initially
        collapsible: true
    };

    // Add section to UI immediately
    const updatedSections = {
        ...sections,
        [sectionId]: newSection
    };

    updateNodeData({
        sections: updatedSections
    });

    // Set loading state immediately
    setLoadingSections(prev => ({ ...prev, [sectionId]: true }));

    // Clear any previous errors
    setGenerationError(prev => {
        const newErrors = { ...prev };
        delete newErrors[sectionId];
        return newErrors;
    });

    // Start content generation in the background
    generateSectionContentOptimistic(
        newSection,
        conceptTitle,
        updatedSections,
        tags,
        nodeId,
        sectionDefaults,
        updateNodeData,
        setLoadingSections,
        setGenerationError
    );

    return newSection;
};

/**
 * Generates content for a section using the optimized API store approach
 */
const generateSectionContentOptimistic = async (
    section: SectionConfig,
    conceptTitle: string,
    sections: Record<string, SectionConfig>,
    tags: { name: string; type: string }[],
    nodeId: string,
    sectionDefaults: any,
    updateNodeData: (data: Record<string, any>) => void,
    setLoadingSections: (updater: (prev: Record<string, boolean>) => Record<string, boolean>) => void,
    setGenerationError: (updater: (prev: Record<string, string>) => Record<string, string>) => void
) => {
    try {
        console.log(`[handleOptimisticSectionAdd] Generating content for section:`, section);

        // Convert sections to the format expected by the API
        const apiSections: Record<string, NodeSection> = {};
        Object.entries(sections).forEach(([key, sectionItem]) => {
            apiSections[key] = {
                id: sectionItem.id,
                label: sectionItem.label,
                icon: sectionItem.icon as string,
                value: sectionItem.value,
                collapsible: sectionItem.collapsible
            };
        });

        // Convert tags to the format expected by the API
        const apiTags: NodeTag[] = tags.map(tag => ({
            name: tag.name,
            type: tag.type
        }));

        // Use the API store for generation
        const apiStore = useApiStore.getState();
        apiStore.setSourceConceptTitle(conceptTitle);
        apiStore.setSourceSections(apiSections);
        apiStore.setRequestType('section');
        apiStore.setSectionType(sectionDefaults.defaultTitle);
        apiStore.setCurrentNodeId(nodeId);

        // Set section-specific temperature and maxTokens if available
        if (sectionDefaults.temperature) {
            apiStore.setTemperature(sectionDefaults.temperature);
        }
        if (sectionDefaults.maxTokens) {
            apiStore.setMaxTokens(sectionDefaults.maxTokens);
        }

        // Generate the content
        await apiStore.generate();

        // Get the generated content and heading
        const generatedContent = apiStore.generatedSection;
        const generatedHeading = apiStore.generatedSectionHeading;

        if (generatedContent) {
            // Use the generated heading if available, otherwise keep the original label
            let finalLabel = section.label;
            if (generatedHeading && generatedHeading !== sectionDefaults.defaultTitle) {
                finalLabel = generatedHeading.length > 50 
                    ? `${generatedHeading.substring(0, 47)}...`
                    : generatedHeading;
            }

            // Create updated section with generated content
            const updatedSection: SectionConfig = {
                id: section.id,
                label: finalLabel,
                icon: section.icon,
                value: generatedContent,
                collapsible: section.collapsible ?? true
            };

            // Update the sections object
            const updatedSections = {
                ...sections,
                [section.id]: updatedSection
            };

            // Update the node data with the new content
            updateNodeData({ sections: updatedSections });

            console.log(`[handleOptimisticSectionAdd] Successfully generated content for section ${section.id}`);
        } else {
            throw new Error('No content was generated');
        }
    } catch (error) {
        console.error(`[handleOptimisticSectionAdd] Error generating section content:`, error);
        setGenerationError(prev => ({
            ...prev,
            [section.id]: 'Failed to generate content. Click to try again.'
        }));
    } finally {
        // Clear loading state
        setLoadingSections(prev => {
            const newLoading = { ...prev };
            delete newLoading[section.id];
            return newLoading;
        });
    }
};
