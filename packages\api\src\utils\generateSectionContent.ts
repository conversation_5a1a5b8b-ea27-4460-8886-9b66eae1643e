import { NodeSection, NodeTag, callChatGPT4oMini } from '../openai';
import { generateSection } from '../prompts/generateSection';
import { useGlobalStore, NODE_SECTIONS } from '@repo/utils';

/**
 * Generates content for a section of a concept node
 *
 * @param conceptTitle - The title of the concept
 * @param sectionLabel - The label of the section to generate content for
 * @param tags - The tags associated with the concept
 * @param sections - The existing sections of the concept
 * @returns The generated content for the section
 */
export async function generateSectionContent(
    conceptTitle: string,
    sectionLabel: string,
    tags: NodeTag[],
    sections: Record<string, NodeSection>
): Promise<string> {
    try {
        // Get global store data
        const {
            activeDeckName: deckTitle,
            activeDeckLearningOutcome: learningOutcome,
            activeDeckLanguage: language
        } = useGlobalStore.getState();

        // Find the section definition to get section-specific settings
        const sectionKey = Object.keys(NODE_SECTIONS).find(
            key => NODE_SECTIONS[key as keyof typeof NODE_SECTIONS].defaultTitle === sectionLabel
        ) as keyof typeof NODE_SECTIONS | undefined;

        // Get section-specific temperature and maxTokens if available
        const sectionTemperature =
            sectionKey && 'temperature' in NODE_SECTIONS[sectionKey]
                ? NODE_SECTIONS[sectionKey].temperature
                : 0.7; // Default temperature

        const sectionMaxTokens =
            sectionKey && 'maxTokens' in NODE_SECTIONS[sectionKey]
                ? NODE_SECTIONS[sectionKey].maxTokens
                : 2500; // Default maxTokens

        console.log(
            `[generateSectionContent] Section: ${sectionLabel}, Key: ${sectionKey}, Temperature: ${sectionTemperature}, MaxTokens: ${sectionMaxTokens}`
        );

        // Generate the prompt for the section
        const prompt = generateSection(
            conceptTitle,
            sectionLabel,
            sections,
            deckTitle || undefined,
            learningOutcome || undefined,
            language || 'en'
        );

        console.log(`[generateSectionContent] Generated prompt for ${sectionLabel}:`, prompt);

        // Call the OpenAI API with section-specific settings
        const response = await callChatGPT4oMini(prompt, {
            temperature: sectionTemperature,
            maxTokens: sectionMaxTokens
        });

        // Process the response
        console.log(`[generateSectionContent] Raw response for ${sectionLabel}:`, response.content);

        const lines = response.content.split('\n');
        let heading = '';
        let content = '';

        if (lines.length > 0) {
            // First line is the heading
            heading = lines[0].trim();

            // Skip the first two lines (heading and blank line) and join the rest
            content = lines.slice(2).join('\n').trim();

            console.log(
                `[generateSectionContent] Generated section heading for ${sectionLabel}:`,
                heading
            );
            console.log(
                `[generateSectionContent] Generated section content for ${sectionLabel}:`,
                content
            );
        } else {
            // Fallback if the format is not as expected
            content = response.content;
            console.log(
                `[generateSectionContent] Using fallback content for ${sectionLabel}:`,
                content
            );
        }

        // Return the content
        if (!content) {
            console.error(`[generateSectionContent] No content was generated for ${sectionLabel}`);
            throw new Error(`No content was generated for section: ${sectionLabel}`);
        }

        console.log(
            `[generateSectionContent] Successfully generated content for ${sectionLabel}, length: ${content.length}`
        );
        return content;
    } catch (error) {
        console.error(
            `[generateSectionContent] Error generating section content for ${sectionLabel}:`,
            error
        );
        throw error;
    }
}
