import { NODE_SECTIONS } from '@repo/utils';
import { handleAddSection } from './handleAddSection';
import { SectionConfig } from '../../../Map/interfaces/SectionConfig';
import { handleOptimisticSectionAdd } from './handleOptimisticSectionAdd';

export const handleAddSectionSelect = async (
    sectionKey: string,
    conceptTitle: string,
    sections: Record<string, SectionConfig>,
    tags: { name: string; type: string }[],
    nodeId: string,
    startEditingSectionLabel: (sectionId: string, label: string) => void,
    generateSectionContentWithLLM: (section: SectionConfig) => Promise<void>,
    updateNodeData: (data: Record<string, any>) => void,
    setSectionSelectPopoverOpen: (open: boolean) => void,
    setLoadingSections?: (
        updater: (prev: Record<string, boolean>) => Record<string, boolean>
    ) => void,
    setGenerationError?: (updater: (prev: Record<string, string>) => Record<string, string>) => void
) => {
    // Close the popover immediately for better UX
    setSectionSelectPopoverOpen(false);

    // Handle custom section creation
    if (sectionKey === 'custom') {
        const customLabel = 'Custom Section';
        const newSection = await handleAddSection(
            startEditingSectionLabel,
            generateSectionContentWithLLM,
            { label: customLabel, icon: '📝' }
        );

        const updatedSections = {
            ...sections,
            [newSection.id]: newSection
        };

        updateNodeData({
            sections: updatedSections
        });

        return;
    }

    const sectionDefaults = NODE_SECTIONS[sectionKey as keyof typeof NODE_SECTIONS];
    if (sectionDefaults) {
        // Use optimistic section addition if loading state setters are available
        if (setLoadingSections && setGenerationError) {
            await handleOptimisticSectionAdd(
                sectionKey,
                conceptTitle,
                sections,
                tags,
                nodeId,
                updateNodeData,
                setLoadingSections,
                setGenerationError
            );
        } else {
            // Fallback to the old method if loading state setters are not available
            const newSection = await handleAddSection(
                startEditingSectionLabel,
                generateSectionContentWithLLM,
                { label: sectionDefaults.defaultTitle, icon: sectionDefaults.icon }
            );

            const updatedSections = {
                ...sections,
                [newSection.id]: newSection
            };

            updateNodeData({
                sections: updatedSections
            });

            // Generate content for the section after adding it
            generateSectionContentWithLLM(newSection);
        }

        return;
    }

    console.warn(`Unknown section key: ${sectionKey}`);
};
