/* Tiptap Editor Styles */

/* Base editor styles */
.tiptap-editor .ProseMirror {
    outline: none;
    width: 100%;
    max-width: 100%;
    min-height: 20px;
    padding: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #1f2937;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Paragraph styles */
.tiptap-editor .ProseMirror p {
    margin: 0.5em 0;
}

.tiptap-editor .ProseMirror p:first-child {
    margin-top: 0;
}

.tiptap-editor .ProseMirror p:last-child {
    margin-bottom: 0;
}

/* List styles */
.tiptap-editor .ProseMirror ul,
.tiptap-editor .ProseMirror ol {
    padding-left: 1.5em;
    margin: 0.5em 0;
}

/* Heading styles */
.tiptap-editor .ProseMirror h1,
.tiptap-editor .ProseMirror h2,
.tiptap-editor .ProseMirror h3,
.tiptap-editor .ProseMirror h4,
.tiptap-editor .ProseMirror h5,
.tiptap-editor .ProseMirror h6 {
    margin: 1em 0 0.5em;
    font-weight: 600;
}

.tiptap-editor .ProseMirror h1 {
    font-size: 1.25rem;
}

.tiptap-editor .ProseMirror h2 {
    font-size: 1.15rem;
}

.tiptap-editor .ProseMirror h3 {
    font-size: 1.05rem;
}

/* Code styles */
.tiptap-editor .ProseMirror code {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25em;
    padding: 0.1em 0.3em;
    font-family: monospace;
    word-break: break-all;
}

.tiptap-editor .ProseMirror pre {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25em;
    padding: 0.5em;
    font-family: monospace;
    overflow-x: auto;
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.tiptap-editor .ProseMirror .code-block-wrapper {
    max-width: 100%;
    overflow: hidden;
}

/* Blockquote styles */
.tiptap-editor .ProseMirror blockquote {
    border-left: 3px solid #e5e7eb;
    padding-left: 1em;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
}

/* Horizontal rule styles */
.tiptap-editor .ProseMirror hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 1em 0;
}

/* Link styles */
.tiptap-editor .ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
}

/* Inline editor content styles */
.tiptap-inline-content {
    outline: none;
    width: 100% !important;
    max-width: 100% !important;
    min-height: 20px;
    padding: 0;
    font-size: 0.8125rem;
    line-height: 1.5;
    color: #1f2937;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
}

/* Renderer content styles */
.tiptap-renderer-content {
    outline: none;
    width: 100% !important;
    max-width: 100% !important;
    min-height: 20px;
    padding: 0;
    font-size: 0.8125rem;
    line-height: 1.5;
    color: #1f2937;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
}

/* Read-only markdown content styles (legacy) */
.markdown-content .ProseMirror {
    outline: none;
    width: 100% !important;
    max-width: 100% !important;
    min-height: 20px;
    padding: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #1f2937;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
}

/* Inline editor paragraph styles */
.tiptap-inline-content p {
    margin: 0.5em 0 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

.tiptap-inline-content p:first-child {
    margin-top: 0 !important;
}

.tiptap-inline-content p:last-child {
    margin-bottom: 0 !important;
}

/* Inline editor list styles */
.tiptap-inline-content ul {
    padding-left: 1.5em !important;
    margin: 0.5em 0 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    list-style-type: disc !important;
}

.tiptap-inline-content ol {
    padding-left: 1.5em !important;
    margin: 0.5em 0 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    list-style-type: decimal !important;
}

.tiptap-inline-content li {
    margin: 0.25em 0 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    display: list-item !important;
}

/* Inline editor heading styles */
.tiptap-inline-content h1,
.tiptap-inline-content h2,
.tiptap-inline-content h3,
.tiptap-inline-content h4,
.tiptap-inline-content h5,
.tiptap-inline-content h6 {
    margin: 1em 0 0.5em !important;
    font-weight: 600 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

.tiptap-inline-content h1 { font-size: 1.25rem !important; }
.tiptap-inline-content h2 { font-size: 1.15rem !important; }
.tiptap-inline-content h3 { font-size: 1.05rem !important; }

/* Inline editor text formatting */
.tiptap-inline-content strong {
    font-weight: 600 !important;
}

.tiptap-inline-content em {
    font-style: italic !important;
}

.tiptap-inline-content code {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-radius: 0.25em !important;
    padding: 0.1em 0.3em !important;
    font-family: monospace !important;
    word-break: break-all !important;
    max-width: 100% !important;
}

.tiptap-inline-content pre {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-radius: 0.25em !important;
    padding: 0.5em !important;
    font-family: monospace !important;
    overflow-x: auto !important;
    max-width: 100% !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
}

.tiptap-inline-content blockquote {
    border-left: 3px solid #e5e7eb !important;
    padding-left: 1em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    font-style: italic !important;
    max-width: 100% !important;
}

/* Renderer content styles */
.tiptap-renderer-content p {
    margin: 0.5em 0 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

.tiptap-renderer-content p:first-child {
    margin-top: 0 !important;
}

.tiptap-renderer-content p:last-child {
    margin-bottom: 0 !important;
}

.tiptap-renderer-content ul {
    padding-left: 1.5em !important;
    margin: 0.5em 0 !important;
    max-width: 100% !important;
    list-style-type: disc !important;
}

.tiptap-renderer-content ol {
    padding-left: 1.5em !important;
    margin: 0.5em 0 !important;
    max-width: 100% !important;
    list-style-type: decimal !important;
}

.tiptap-renderer-content li {
    margin: 0.25em 0 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    display: list-item !important;
}

.tiptap-renderer-content h1,
.tiptap-renderer-content h2,
.tiptap-renderer-content h3,
.tiptap-renderer-content h4,
.tiptap-renderer-content h5,
.tiptap-renderer-content h6 {
    margin: 1em 0 0.5em !important;
    font-weight: 600 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

.tiptap-renderer-content h1 { font-size: 1.25rem !important; }
.tiptap-renderer-content h2 { font-size: 1.15rem !important; }
.tiptap-renderer-content h3 { font-size: 1.05rem !important; }

.tiptap-renderer-content strong {
    font-weight: 600 !important;
}

.tiptap-renderer-content em {
    font-style: italic !important;
}

.tiptap-renderer-content code {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-radius: 0.25em !important;
    padding: 0.1em 0.3em !important;
    font-family: monospace !important;
    word-break: break-all !important;
    max-width: 100% !important;
}

.tiptap-renderer-content pre {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-radius: 0.25em !important;
    padding: 0.5em !important;
    font-family: monospace !important;
    overflow-x: auto !important;
    max-width: 100% !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
}

.tiptap-renderer-content blockquote {
    border-left: 3px solid #e5e7eb !important;
    padding-left: 1em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    font-style: italic !important;
    max-width: 100% !important;
}

/* Legacy markdown content styles */
.markdown-content .ProseMirror p {
    margin: 0.5em 0;
}

.markdown-content .ProseMirror p:first-child {
    margin-top: 0;
}

.markdown-content .ProseMirror p:last-child {
    margin-bottom: 0;
}

.markdown-content .ProseMirror ul,
.markdown-content .ProseMirror ol {
    padding-left: 1.5em;
    margin: 0.5em 0;
}

.markdown-content .ProseMirror h1,
.markdown-content .ProseMirror h2,
.markdown-content .ProseMirror h3,
.markdown-content .ProseMirror h4,
.markdown-content .ProseMirror h5,
.markdown-content .ProseMirror h6 {
    margin: 1em 0 0.5em;
    font-weight: 600;
}

.markdown-content .ProseMirror h1 {
    font-size: 1.25rem;
}

.markdown-content .ProseMirror h2 {
    font-size: 1.15rem;
}

.markdown-content .ProseMirror h3 {
    font-size: 1.05rem;
}

.markdown-content .ProseMirror code {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25em;
    padding: 0.1em 0.3em;
    font-family: monospace;
    word-break: break-all;
}

.markdown-content .ProseMirror pre {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25em;
    padding: 0.5em;
    font-family: monospace;
    overflow-x: auto;
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.markdown-content .ProseMirror .code-block-wrapper {
    max-width: 100%;
    overflow: hidden;
}

.markdown-content .ProseMirror blockquote {
    border-left: 3px solid #e5e7eb;
    padding-left: 1em;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
}

.markdown-content .ProseMirror hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 1em 0;
}

.markdown-content .ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
}

/* Tiptap v3 specific styles */
.tiptap-editor .is-editor-empty:first-child::before,
.markdown-content .is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #adb5bd;
    pointer-events: none;
    height: 0;
}

/* Ensure proper focus styles */
.tiptap-editor .ProseMirror:focus,
.markdown-content .ProseMirror:focus {
    outline: none;
}

/* Ensure proper cursor in editable areas */
.tiptap-editor .ProseMirror {
    cursor: text;
}

/* Ensure proper cursor in read-only areas */
.markdown-content .ProseMirror {
    cursor: text;
}

/* Inline editor specific styles */
.tiptap-inline-editor {
    cursor: text;
    max-width: 100% !important;
    width: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
}

.tiptap-inline-editor .ProseMirror {
    cursor: text;
    max-width: 100% !important;
    width: 100% !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
}

.tiptap-inline-editor:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Ensure all content respects container width */
.tiptap-inline-editor .ProseMirror * {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
}

/* Specific handling for code blocks and pre elements */
.tiptap-inline-editor .ProseMirror pre,
.tiptap-inline-editor .ProseMirror code {
    max-width: 100% !important;
    overflow-x: auto !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    box-sizing: border-box !important;
}

/* Force all child elements to respect width */
.tiptap-inline-editor .ProseMirror p,
.tiptap-inline-editor .ProseMirror div,
.tiptap-inline-editor .ProseMirror span,
.tiptap-inline-editor .ProseMirror h1,
.tiptap-inline-editor .ProseMirror h2,
.tiptap-inline-editor .ProseMirror h3,
.tiptap-inline-editor .ProseMirror h4,
.tiptap-inline-editor .ProseMirror h5,
.tiptap-inline-editor .ProseMirror h6,
.tiptap-inline-editor .ProseMirror blockquote {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
}

/* Specific list styling for inline editor */
.tiptap-inline-editor .ProseMirror ul {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
    list-style-type: disc !important;
    padding-left: 1.5em !important;
    margin: 0.5em 0 !important;
}

.tiptap-inline-editor .ProseMirror ol {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
    list-style-type: decimal !important;
    padding-left: 1.5em !important;
    margin: 0.5em 0 !important;
}

.tiptap-inline-editor .ProseMirror li {
    max-width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    box-sizing: border-box !important;
    display: list-item !important;
    margin: 0.25em 0 !important;
}

/* ConceptNode width enforcement */
.concept-node-container {
    max-width: 440px !important;
    width: 440px !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.concept-node-container * {
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}
